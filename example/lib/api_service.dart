import 'dart:convert';
import 'package:http/http.dart' as http;

/// A utility class for making API requests
class ApiService {
  /// Base URL for the API
  static const String baseUrl = 'http://192.168.31.170:8080';

  /// Sends a POST request to the specified API endpoint with JSON data
  ///
  /// [endpoint] - The API endpoint to send the request to (e.g., '/api/report')
  /// [data] - The data to send in the request body as a Map
  /// [headers] - Optional additional headers to include in the request
  ///
  /// Returns a Future with the response
  static Future<http.Response> post(String endpoint, Map<String, dynamic> data, {Map<String, String>? headers}) async {
    final url = Uri.parse('$baseUrl$endpoint');
    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        ...?headers,
      },
      body: jsonEncode(data),
    );
    return response;
  }

  /// Registers a notification token for a user
  static Future<http.Response> registerNotificationToken({
    required int userId,
    required String notificationToken,
    required String platform,
  }) async {
    return post(
      '/api/v1/notification/token',
      {
        'user_id': userId,
        'notification_token': notificationToken,
        'platform': platform,
      },
    );
  }

  /// Initiates a new call session between two users
  static Future<http.Response> initiateCall({
    required int callerId,
    required int calleeId,
  }) async {
    return post(
      '/api/v1/call/initiate',
      {
        'caller_id': callerId,
        'callee_id': calleeId,
      },
    );
  }
}
