import 'dart:convert';
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_callkit_incoming/entities/entities.dart';
import 'package:flutter_callkit_incoming/flutter_callkit_incoming.dart';
import 'package:flutter_callkit_incoming_example/navigation_service.dart';
import 'package:flutter_callkit_incoming_example/api_service.dart';
import 'package:http/http.dart';

class CallingPage extends StatefulWidget {
  final Object? args;
  const CallingPage({Key? key, this.args}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return CallingPageState();
  }
}

class CallingPageState extends State<CallingPage> {
  late CallKitParams? calling;
  late dynamic callArgs;
  String channelName = '';
  String agoraToken = '';
  String appId = '';

  Timer? _timer;
  int _start = 0;

  @override
  void initState() {
    super.initState();
    callArgs = widget.args;
    print("callArgs $callArgs");
    // Extract channelName, agoraToken, appId from callArgs['extra'] if available

    if (callArgs != null && callArgs is Map && callArgs['extra'] is Map) {
      final extra = callArgs['extra'] as Map;
      channelName = extra['channelName']?.toString() ?? '';
      agoraToken = extra['agoraToken']?.toString() ?? '';
      appId = extra['appId']?.toString() ?? '';
    }
    // You can now use callArgs in your widget
    startTimer();
  }

  void startTimer() {
    const oneSec = Duration(seconds: 1);
    _timer = Timer.periodic(
      oneSec,
      (Timer timer) {
        setState(() {
          _start++;
        });
      },
    );
  }

  String intToTimeLeft(int value) {
    int h, m, s;
    h = value ~/ 3600;
    m = ((value - h * 3600)) ~/ 60;
    s = value - (h * 3600) - (m * 60);
    String hourLeft = h.toString().length < 2 ? '0$h' : h.toString();
    String minuteLeft = m.toString().length < 2 ? '0$m' : m.toString();
    String secondsLeft = s.toString().length < 2 ? '0$s' : s.toString();
    String result = "$hourLeft:$minuteLeft:$secondsLeft";
    return result;
  }

  @override
  Widget build(BuildContext context) {
    final params = jsonDecode(jsonEncode(
        ModalRoute.of(context)!.settings.arguments as Map<dynamic, dynamic>));
    print(ModalRoute.of(context)!.settings.arguments);
    calling = CallKitParams.fromJson(params);

    var timeDisplay = intToTimeLeft(_start);

    return Scaffold(
      body: SizedBox(
        height: MediaQuery.of(context).size.height,
        width: double.infinity,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(timeDisplay),
              const Text('Calling...'),
              if (channelName.isNotEmpty || agoraToken.isNotEmpty || appId.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Column(
                    children: [
                      if (channelName.isNotEmpty) Text('Channel Name: $channelName'),
                      if (agoraToken.isNotEmpty) Text('Agora Token: $agoraToken'),
                      if (appId.isNotEmpty) Text('App ID: $appId'),
                    ],
                  ),
                ),
              TextButton(
                style: ButtonStyle(
                  foregroundColor:
                      MaterialStateProperty.all<Color>(Colors.blue),
                ),
                onPressed: () async {
                  if (calling != null) {
                    await makeFakeConnectedCall(calling!.id!);
                    startTimer();
                  }
                },
                child: const Text('Fake Connected Call'),
              ),
              TextButton(
                style: ButtonStyle(
                  foregroundColor:
                      MaterialStateProperty.all<Color>(Colors.blue),
                ),
                onPressed: () async {
                  if (calling != null) {
                    await makeEndCall(calling!.id!);
                    calling = null;
                  }
                  NavigationService.instance.goBack();
                },
                child: const Text('End Call'),
              )
            ],
          ),
        ),
      ),
    );
  }

  Future<void> makeFakeConnectedCall(id) async {
    await FlutterCallkitIncoming.setCallConnected(id);
  }

  Future<void> makeEndCall(id) async {
    await FlutterCallkitIncoming.endCall(id);
  }

  @override
  void dispose() {
    super.dispose();
    _timer?.cancel();
    if (calling != null) FlutterCallkitIncoming.endCall(calling!.id!);
  }
}
