import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_callkit_incoming/entities/android_params.dart';
import 'package:flutter_callkit_incoming/entities/call_event.dart';
import 'package:flutter_callkit_incoming/entities/call_kit_params.dart';
import 'package:flutter_callkit_incoming/entities/ios_params.dart';
import 'package:flutter_callkit_incoming/entities/notification_params.dart';
import 'package:flutter_callkit_incoming/flutter_callkit_incoming.dart';
import 'package:flutter_callkit_incoming_example/app_router.dart';
import 'package:flutter_callkit_incoming_example/navigation_service.dart';
import 'package:flutter_callkit_incoming_example/api_service.dart';
import 'package:uuid/uuid.dart';

final userId = Platform.isAndroid ? 1111 : 2222;


class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<StatefulWidget> createState() {
    return HomePageState();
  }
}

class HomePageState extends State<HomePage> {
  late final Uuid _uuid;
  String? _currentUuid;
  String textEvents = "";
  String? _registerTokenStatus;
  final TextEditingController _calleeController = TextEditingController();
  String? _callStatus;

  @override
  void initState() {
    super.initState();
    _uuid = const Uuid();
    _currentUuid = "";
    textEvents = "";
    initCurrentCall();
    listenerEvent(onEvent);
    _registerPushToken();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Plugin example app'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(10.0),
        child: Column(
          children: [
            Text('Your Id: $userId'),
            if (_registerTokenStatus != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Text(
                  _registerTokenStatus!,
                  style: TextStyle(
                    color: _registerTokenStatus!.contains('success') ? Colors.green : Colors.red,
                  ),
                ),
              ),
            ElevatedButton(
              onPressed: _registerPushToken,
              child: const Text('Register Push Token'),
            ),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _calleeController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'Callee ID',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _initiateCall,
                  child: const Text('Call'),
                ),
              ],
            ),
            if (_callStatus != null)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Text(
                  _callStatus!,
                  style: TextStyle(
                    color: _callStatus!.contains('success') ? Colors.green : Colors.red,
                  ),
                ),
              ),
            Column(
              children: [
                ElevatedButton.icon(
                  icon: const Icon(Icons.clear_all_sharp),
                  label: Text('End all calls'),
                  onPressed: endAllCalls,
                ),
                const Divider(),
              ],
            ),
            Expanded(
              child: LayoutBuilder(
                builder:
                    (BuildContext context, BoxConstraints viewportConstraints) {
                  if (textEvents.isNotEmpty) {
                    return SingleChildScrollView(
                      child: ConstrainedBox(
                        constraints: BoxConstraints(
                          minHeight: viewportConstraints.maxHeight,
                        ),
                        child: Text(textEvents),
                      ),
                    );
                  } else {
                    return const Center(
                      child: Text('No Event'),
                    );
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> requestNotificationPermission() async {
    await FlutterCallkitIncoming.requestNotificationPermission({
      "rationaleMessagePermission":
          "Notification permission is required, to show notification.",
      "postNotificationMessageRequired":
          "Notification permission is required, Please allow notification permission from setting."
    });
  }

  Future<dynamic> initCurrentCall() async {
    await requestNotificationPermission();
    //check current call from pushkit if possible
    var calls = await FlutterCallkitIncoming.activeCalls();
    if (calls is List) {
      if (calls.isNotEmpty) {
        print('DATA: $calls');
        _currentUuid = calls[0]['id'];
        return calls[0];
      } else {
        _currentUuid = "";
        return null;
      }
    }
  }

  Future<void> makeFakeCallInComing() async {
    await Future.delayed(const Duration(seconds: 10), () async {
      _currentUuid = _uuid.v4();

      final params = CallKitParams(
        id: _currentUuid,
        nameCaller: 'Hien Nguyen',
        appName: 'Callkit',
        avatar: 'https://i.pravatar.cc/100',
        handle: '0123456789',
        type: 0,
        duration: 30000,
        textAccept: 'Accept',
        textDecline: 'Decline',
        missedCallNotification: const NotificationParams(
          showNotification: true,
          isShowCallback: true,
          subtitle: 'Missed call',
          callbackText: 'Call back',
        ),
        callingNotification: const NotificationParams(
          showNotification: true,
          isShowCallback: true,
          subtitle: 'Calling...',
          callbackText: 'Hang Up',
        ),
        extra: <String, dynamic>{'userId': '1a2b3c4d'},
        headers: <String, dynamic>{'apiKey': 'Abc@123!', 'platform': 'flutter'},
        android: const AndroidParams(
          isCustomNotification: true,
          isShowLogo: true,
          isShowCallID: true,
          logoUrl: 'assets/test.png',
          ringtonePath: 'system_ringtone_default',
          backgroundColor: '#0955fa',
          backgroundUrl: 'assets/test.png',
          actionColor: '#4CAF50',
          textColor: '#ffffff',
          incomingCallNotificationChannelName: 'Incoming Call',
          missedCallNotificationChannelName: 'Missed Call',
          isImportant: true,
          isBot: false,
        ),
        ios: const IOSParams(
          iconName: 'CallKitLogo',
          handleType: '',
          supportsVideo: true,
          maximumCallGroups: 2,
          maximumCallsPerCallGroup: 1,
          audioSessionMode: 'default',
          audioSessionActive: true,
          audioSessionPreferredSampleRate: 44100.0,
          audioSessionPreferredIOBufferDuration: 0.005,
          supportsDTMF: true,
          supportsHolding: true,
          supportsGrouping: false,
          supportsUngrouping: false,
          ringtonePath: 'system_ringtone_default',
        ),
      );
      await FlutterCallkitIncoming.showCallkitIncoming(params);
    });
  }

  Future<void> endCurrentCall() async {
    initCurrentCall();
    await FlutterCallkitIncoming.endCall(_currentUuid!);
  }

  Future<void> activeCalls() async {
    var calls = await FlutterCallkitIncoming.activeCalls();
    print(calls);
  }

  Future<void> endAllCalls() async {
    await FlutterCallkitIncoming.endAllCalls();
  }

  Future<void> getDevicePushTokenVoIP() async {
    var devicePushTokenVoIP =
        await FlutterCallkitIncoming.getDevicePushTokenVoIP();
    print(devicePushTokenVoIP);
  }

  Future<void> listenerEvent(void Function(CallEvent) callback) async {
    try {
      FlutterCallkitIncoming.onEvent.listen((event) async {
        switch (event!.event) {
          case Event.actionCallIncoming:
            // TODO: received an incoming call
            break;
          case Event.actionCallStart:
            // TODO: started an outgoing call
            // TODO: show screen calling in Flutter
            break;
          case Event.actionCallAccept:
            // TODO: accepted an incoming call
            // TODO: show screen calling in Flutter
            NavigationService.instance
                .pushNamedIfNotCurrent(AppRoute.callingPage, args: event.body);
            break;
          case Event.actionCallDecline:
            // TODO: declined an incoming call
            break;
          case Event.actionCallEnded:
            // TODO: ended an incoming/outgoing call
            break;
          case Event.actionCallTimeout:
            // TODO: missed an incoming call
            break;
          case Event.actionCallCallback:
            // TODO: only Android - click action `Call back` from missed call notification
            break;
          case Event.actionCallToggleHold:
            // TODO: only iOS
            break;
          case Event.actionCallToggleMute:
            // TODO: only iOS
            break;
          case Event.actionCallToggleDmtf:
            // TODO: only iOS
            break;
          case Event.actionCallToggleGroup:
            // TODO: only iOS
            break;
          case Event.actionCallToggleAudioSession:
            // TODO: only iOS
            break;
          case Event.actionDidUpdateDevicePushTokenVoip:
            // TODO: only iOS
            break;
          case Event.actionCallCustom:
            break;
        }
        callback(event);
      });
    } on Exception catch (e) {
      print(e);
    }
  }

  Future<void> _registerPushToken() async {
    await Future.delayed(Duration(seconds: 1));
    String? token;
    String platform;
    if (Platform.isAndroid) {
      token = await FirebaseMessaging.instance.getToken();
      platform = "android";
    } else {
      token = await FlutterCallkitIncoming.getDevicePushTokenVoIP();
      platform = "ios";
    }
    if (token != null) {
      try {
        final response = await ApiService.registerNotificationToken(
          userId: userId, // Use a random int for user_id
          notificationToken: token,
          platform: platform,
        );
        if (response.statusCode == 200) {
          setState(() {
            _registerTokenStatus = "Push token registered successfully";
          });
          debugPrint("Push token registered successfully");
        } else {
          setState(() {
            _registerTokenStatus = "Failed to register push token: ${response.body}";
          });
          debugPrint("Failed to register push token ${response.body}");
        }
      } catch (e) {
        setState(() {
          _registerTokenStatus = "Error registering push token: $e";
        });
        debugPrint("Error registering push token: $e");
      }
    }
  }

  Future<void> _initiateCall() async {
    final calleeIdText = _calleeController.text.trim();
    if (calleeIdText.isEmpty || int.tryParse(calleeIdText) == null) {
      setState(() {
        _callStatus = 'Please enter a valid callee ID.';
      });
      return;
    }
    final calleeId = int.parse(calleeIdText);
    setState(() {
      _callStatus = 'Calling...';
    });
    try {
      final response = await ApiService.initiateCall(
        callerId: userId,
        calleeId: calleeId,
      );
      if (response.statusCode == 200) {
        setState(() {
          _callStatus = 'Call initiated successfully!';
        });

        // Parse the API response and navigate to calling page
        try {
          final responseData = jsonDecode(response.body);
          final callId = responseData['call_id'] as String;
          final channelName = responseData['channel_name'] as String;
          final callerToken = responseData['caller_token'] as String;
          final appId = responseData['app_id'] as String;

          // Create CallKit params for the outgoing call
          final params = CallKitParams(
            id: callId,
            nameCaller: 'You',
            appName: 'Callkit',
            avatar: 'https://i.pravatar.cc/100',
            handle: calleeIdText,
            type: 0, // Audio call
            duration: 30000,
            textAccept: 'Accept',
            textDecline: 'Decline',
            extra: <String, dynamic>{
              'channelName': channelName,
              'agoraToken': callerToken,
              'appId': appId,
              'platform': 'flutter'
            },
            headers: <String, dynamic>{'apiKey': 'Abc@123!', 'platform': 'flutter'},
            android: const AndroidParams(
              isCustomNotification: true,
              isShowLogo: true,
              isShowCallID: true,
              logoUrl: 'assets/test.png',
              ringtonePath: 'system_ringtone_default',
              backgroundColor: '#0955fa',
              backgroundUrl: 'assets/test.png',
              actionColor: '#4CAF50',
              textColor: '#ffffff',
              incomingCallNotificationChannelName: 'Incoming Call',
              missedCallNotificationChannelName: 'Missed Call',
              isImportant: true,
              isBot: false,
            ),
            ios: const IOSParams(
              iconName: 'CallKitLogo',
              handleType: '',
              supportsVideo: false,
              maximumCallGroups: 2,
              maximumCallsPerCallGroup: 1,
              audioSessionMode: 'default',
              audioSessionActive: true,
              audioSessionPreferredSampleRate: 44100.0,
              audioSessionPreferredIOBufferDuration: 0.005,
              supportsDTMF: true,
              supportsHolding: true,
              supportsGrouping: false,
              supportsUngrouping: false,
              ringtonePath: 'system_ringtone_default',
            ),
          );

          // Navigate directly to calling page
          NavigationService.instance.pushNamedIfNotCurrent(
            AppRoute.callingPage,
            args: params.toJson()
          );

        } catch (e) {
          setState(() {
            _callStatus = 'Error parsing response: $e';
          });
        }
      } else {
        setState(() {
          _callStatus = 'Failed to initiate call: ${response.body}';
        });
      }
    } catch (e) {
      setState(() {
        _callStatus = 'Error initiating call: $e';
      });
    }
  }

  void onEvent(CallEvent event) {
    if (!mounted) return;
    setState(() {
      textEvents += '---\n${event.toString()}\n';
    });
  }
}
