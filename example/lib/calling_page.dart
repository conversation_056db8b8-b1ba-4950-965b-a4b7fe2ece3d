import 'dart:convert';
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_callkit_incoming/entities/entities.dart';
import 'package:flutter_callkit_incoming/flutter_callkit_incoming.dart';
import 'package:flutter_callkit_incoming_example/navigation_service.dart';
import 'package:flutter_callkit_incoming_example/home_page.dart';
import 'package:agora_rtc_engine/agora_rtc_engine.dart';


class CallingPage extends StatefulWidget {
  final Object? args;
  const CallingPage({Key? key, this.args}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return CallingPageState();
  }
}

class CallingPageState extends State<CallingPage> {
  late CallKitParams? calling;
  late dynamic callArgs;
  String channelName = '';
  String agoraToken = '';
  String appId = '';

  Timer? _timer;
  int _start = 0;

  // Agora related variables
  RtcEngine? _engine;
  bool _isJoined = false;
  bool _isMuted = false;
  bool _isSpeakerOn = false;
  String _agoraStatus = 'Initializing...';

  @override
  void initState() {
    super.initState();
    callArgs = widget.args;
    print("callArgs $callArgs");
    // Extract channelName, agoraToken, appId from callArgs['extra'] if available

    if (callArgs != null && callArgs is Map && callArgs['extra'] is Map) {
      final extra = callArgs['extra'] as Map;
      channelName = extra['channelName']?.toString() ?? '';
      agoraToken = extra['agoraToken']?.toString() ?? '';
      appId = extra['appId']?.toString() ?? '';
    }
    // You can now use callArgs in your widget
    startTimer();

    // Initialize Agora if we have the required parameters
    if (appId.isNotEmpty && channelName.isNotEmpty) {
      _initializeAgora();
    }
  }

  void startTimer() {
    const oneSec = Duration(seconds: 1);
    _timer = Timer.periodic(
      oneSec,
      (Timer timer) {
        setState(() {
          _start++;
        });
      },
    );
  }

  String intToTimeLeft(int value) {
    int h, m, s;
    h = value ~/ 3600;
    m = ((value - h * 3600)) ~/ 60;
    s = value - (h * 3600) - (m * 60);
    String hourLeft = h.toString().length < 2 ? '0$h' : h.toString();
    String minuteLeft = m.toString().length < 2 ? '0$m' : m.toString();
    String secondsLeft = s.toString().length < 2 ? '0$s' : s.toString();
    String result = "$hourLeft:$minuteLeft:$secondsLeft";
    return result;
  }

  @override
  Widget build(BuildContext context) {
    final params = jsonDecode(jsonEncode(
        ModalRoute.of(context)!.settings.arguments as Map<dynamic, dynamic>));
    print(ModalRoute.of(context)!.settings.arguments);
    calling = CallKitParams.fromJson(params);

    var timeDisplay = intToTimeLeft(_start);

    return Scaffold(
      body: SizedBox(
        height: MediaQuery.of(context).size.height,
        width: double.infinity,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(timeDisplay),
              const Text('Calling...'),
              if (channelName.isNotEmpty || agoraToken.isNotEmpty || appId.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Column(
                    children: [
                      if (channelName.isNotEmpty) Text('Channel Name: $channelName'),
                      if (agoraToken.isNotEmpty) Text('Agora Token: ${agoraToken.length > 20 ? agoraToken.substring(0, 20) + '...' : agoraToken}'),
                      if (appId.isNotEmpty) Text('App ID: $appId'),
                      Text('User ID: $userId'),
                      Text('Status: $_agoraStatus'),
                    ],
                  ),
                ),
              // Agora control buttons
              if (_isJoined) ...[
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      onPressed: _toggleMute,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _isMuted ? Colors.red : Colors.green,
                      ),
                      child: Icon(_isMuted ? Icons.mic_off : Icons.mic),
                    ),
                    ElevatedButton(
                      onPressed: _toggleSpeaker,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _isSpeakerOn ? Colors.blue : Colors.grey,
                      ),
                      child: Icon(_isSpeakerOn ? Icons.volume_up : Icons.volume_down),
                    ),
                  ],
                ),
              ],
              TextButton(
                style: ButtonStyle(
                  foregroundColor:
                      WidgetStateProperty.all<Color>(Colors.red),
                ),
                onPressed: () async {
                  // Leave Agora channel first
                  await _leaveChannel();

                  if (calling != null) {
                    await makeEndCall(calling!.id!);
                    calling = null;
                  }
                  NavigationService.instance.goBack();
                },
                child: const Text('End Call'),
              )
            ],
          ),
        ),
      ),
    );
  }

  Future<void> makeFakeConnectedCall(id) async {
    await FlutterCallkitIncoming.setCallConnected(id);
  }

  Future<void> makeEndCall(id) async {
    await FlutterCallkitIncoming.endCall(id);
  }

  // Agora related methods
  Future<void> _initializeAgora() async {
    try {
      setState(() {
        _agoraStatus = 'Initializing Agora...';
      });

      // Create RTC engine
      _engine = createAgoraRtcEngine();
      await _engine!.initialize(RtcEngineContext(
        appId: appId,
        channelProfile: ChannelProfileType.channelProfileCommunication,
      ));

      // Set up event handlers
      _engine!.registerEventHandler(
        RtcEngineEventHandler(
          onJoinChannelSuccess: (RtcConnection connection, int elapsed) {
            print('Successfully joined channel: ${connection.channelId}');
            setState(() {
              _isJoined = true;
              _agoraStatus = 'Connected to call';
            });
          },
          onUserJoined: (RtcConnection connection, int remoteUid, int elapsed) {
            print('Remote user $remoteUid joined');
            setState(() {
              _agoraStatus = 'User joined the call';
            });
          },
          onUserOffline: (RtcConnection connection, int remoteUid, UserOfflineReasonType reason) {
            print('Remote user $remoteUid left channel');
            setState(() {
              _agoraStatus = 'User left the call';
            });
          },
          onLeaveChannel: (RtcConnection connection, RtcStats stats) {
            print('Left channel');
            setState(() {
              _isJoined = false;
              _agoraStatus = 'Call ended';
            });
          },
          onError: (ErrorCodeType err, String msg) {
            print('Agora error: $err - $msg');
            setState(() {
              _agoraStatus = 'Error: $msg';
            });
          },
        ),
      );

      // Enable audio
      await _engine!.enableAudio();

      // Join channel
      await _joinChannel();

    } catch (e) {
      print('Error initializing Agora: $e');
      setState(() {
        _agoraStatus = 'Failed to initialize: $e';
      });
    }
  }

  Future<void> _joinChannel() async {
    try {
      setState(() {
        _agoraStatus = 'Joining channel...';
      });

      ChannelMediaOptions options = const ChannelMediaOptions(
        clientRoleType: ClientRoleType.clientRoleBroadcaster,
        channelProfile: ChannelProfileType.channelProfileCommunication,
      );

      await _engine!.joinChannel(
        token: agoraToken.isNotEmpty ? agoraToken : "",
        channelId: channelName,
        uid: userId,
        options: options,
      );
    } catch (e) {
      print('Error joining channel: $e');
      setState(() {
        _agoraStatus = 'Failed to join: $e';
      });
    }
  }

  Future<void> _leaveChannel() async {
    try {
      await _engine?.leaveChannel();
      setState(() {
        _isJoined = false;
        _agoraStatus = 'Left channel';
      });
    } catch (e) {
      print('Error leaving channel: $e');
    }
  }

  Future<void> _toggleMute() async {
    try {
      await _engine?.muteLocalAudioStream(!_isMuted);
      setState(() {
        _isMuted = !_isMuted;
      });
    } catch (e) {
      print('Error toggling mute: $e');
    }
  }

  Future<void> _toggleSpeaker() async {
    try {
      await _engine?.setEnableSpeakerphone(!_isSpeakerOn);
      setState(() {
        _isSpeakerOn = !_isSpeakerOn;
      });
    } catch (e) {
      print('Error toggling speaker: $e');
    }
  }

  @override
  void dispose() {
    super.dispose();
    _timer?.cancel();
    if (calling != null) FlutterCallkitIncoming.endCall(calling!.id!);

    // Clean up Agora resources
    _leaveChannel();
    _engine?.release();
  }
}
