package com.example.flutter_callkit_incoming_example

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import com.onesignal.notifications.INotification
import com.onesignal.notifications.INotificationReceivedEvent
import com.onesignal.notifications.INotificationServiceExtension
import org.json.JSONObject


class NotificationServiceExtension: INotificationServiceExtension {
    companion object {
        const val FIREBASE_MESSAGING_ACTION = "com.google.android.c2dm.intent.RECEIVE"
        const val FIREBASE_MESSAGING_RECEIVER = "io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
    }

    override fun onNotificationReceived(event: INotificationReceivedEvent) {
        val notification = event.notification
        val context = event.context

        Log.d("OneSignalForward", "OneSignal notification received")
        Log.d("OneSignalForward", "Title: ${notification.title}")
        Log.d("OneSignalForward", "Body: ${notification.body}")
        Log.d("OneSignalForward", "Notification ID: ${notification.notificationId}")

        // Create Firebase Cloud Messaging compatible intent
        val firebaseIntent = Intent(FIREBASE_MESSAGING_ACTION).apply {
            // Set the component to target FlutterFirebaseMessagingReceiver
            component = ComponentName(context.packageName, FIREBASE_MESSAGING_RECEIVER)

            // Create Firebase-compatible extras bundle
            val extras = Bundle().apply {
                // Firebase messaging format - notification data
                notification.title?.let { putString("gcm.notification.title", it) }
                notification.body?.let { putString("gcm.notification.body", it) }

                // Add OneSignal notification ID as message ID
                putString("google.message_id", notification.notificationId)

                // Add timestamp (Firebase expects this in milliseconds)
                putString("google.sent_time", System.currentTimeMillis().toString())

                // Add required Firebase fields
                putString("from", "OneSignal")
                putString("collapse_key", "onesignal_notification")

                // Add message type to indicate this is a notification
                putString("google.message_type", "gcm")

                // Add custom data fields - these will be available in RemoteMessage.getData()
                notification.additionalData?.let { additionalData ->
                    val keys = additionalData.keys()
                    while (keys.hasNext()) {
                        val key = keys.next()
                        try {
                            val value = additionalData.get(key)
                            putString(key, value.toString())
                        } catch (e: Exception) {
                            Log.w("OneSignalForward", "Error extracting additional data key: $key", e)
                        }
                    }
                }

                // Add OneSignal specific metadata as data fields
                putString("onesignal_notification_id", notification.notificationId)
                notification.launchURL?.let { putString("onesignal_launch_url", it) }
                notification.sound?.let { putString("onesignal_sound", it) }
                putString("onesignal_priority", notification.priority.toString())
                putString("onesignal_source", "true")
            }

            // Add the extras to the intent
            putExtras(extras)
        }
        // Send to FlutterFirebaseMessagingReceiver
        try {
            context.sendBroadcast(firebaseIntent)
            Log.d("OneSignalForward", "OneSignal notification forwarded to FlutterFirebaseMessagingReceiver successfully")
            Log.d("OneSignalForward", "Intent action: ${firebaseIntent.action}")
            Log.d("OneSignalForward", "Intent component: ${firebaseIntent.component}")
            Log.d("OneSignalForward", "Intent extras count: ${firebaseIntent.extras?.size()}")
        } catch (e: Exception) {
            Log.e("OneSignalForward", "Error forwarding notification to FlutterFirebaseMessagingReceiver", e)
        }
    }
}